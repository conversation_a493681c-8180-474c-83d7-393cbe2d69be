# How to Edit Your Website

This is your website. Here's how to change it.

## 🎯 Quick Start - Change Content

### Change Text on Any Page
1. Go to `src/pages/` folder
2. Open the page you want to edit:
   - `Home.tsx` - Homepage
   - `About.tsx` - About page
   - `Portfolio.tsx` - Portfolio page
   - `Contact.tsx` - Contact page
3. Find the text between `>` and `<` and change it
4. Save the file

**Example:** In `src/pages/Home.tsx`, change line 9:
```jsx
<h1 className="hero-title">Welcome to My Awesome Company</h1>
```
Change "Welcome to My Awesome Company" to whatever you want.

### Add New Pages
1. Copy any file from `src/pages/`
2. Rename it (like `Services.tsx`)
3. Edit the content
4. Add it to the menu in `src/components/Navigation.tsx`

## 🎨 Change How Things Look

### Colors and Fonts
**File:** `src/styles/themes.css`

**Change main colors:**
```css
:root {
  --bg-primary: #f8f9fa;     /* Background color */
  --text-primary: #333333;   /* Text color */
  --accent-gradient: linear-gradient(45deg, #00ff88, #00bbff); /* Accent colors */
}
```

**Change fonts:**
```css
body {
  font-family: 'Arial', sans-serif;  /* Change to any font */
}
```

### Layout and Spacing
**File:** `src/pages/Pages.css`

**Make things bigger/smaller:**
```css
.hero-title {
  font-size: 3rem;  /* Change this number */
}
```

**Move sections around:**
Cut and paste the HTML blocks in any `.tsx` file.

## 📱 Responsive Design (Mobile/Desktop)

### Font Sizes for Different Screens
**In `src/pages/Pages.css`, add:**
```css
/* Mobile phones */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;  /* Smaller on mobile */
  }
}

/* Tablets */
@media (max-width: 1024px) {
  .hero-title {
    font-size: 2.5rem;  /* Medium on tablet */
  }
}

/* Desktop */
@media (min-width: 1025px) {
  .hero-title {
    font-size: 4rem;  /* Larger on desktop */
  }
}
```

### Move Things to Bottom
**Example: Move stats section to bottom of page**

1. Open `src/pages/Home.tsx`
2. Find lines 44-66 (the stats section)
3. Cut those lines (Ctrl+X)
4. Scroll to the bottom, before the last `</div>`
5. Paste (Ctrl+V)
6. Save

**Before:**
```jsx
<div className="features-section">...</div>
<div className="stats-section">...</div>  ← This moves
</div>
```

**After:**
```jsx
<div className="features-section">...</div>
</div>
<div className="stats-section">...</div>  ← Now at bottom
```

## 🌙 Fix Dark/Light Mode

### The Theme Toggle is Backwards
**File:** `src/components/ThemeToggle.tsx`

**Line 25, change:**
```jsx
{isDark ? '☀️ Light' : '🌙 Dark'}
```
**To:**
```jsx
{isDark ? '🌙 Dark' : '☀️ Light'}
```

### Change Dark Mode Colors
**File:** `src/styles/themes.css`

**Find `[data-theme="dark"]` and change:**
```css
[data-theme="dark"] {
  --bg-primary: #000000;     /* Make darker */
  --text-primary: #ffffff;   /* Make text white */
}
```

## 🔧 Common Fixes

### Remove the "Kindergarten" Style
**File:** `src/pages/Pages.css`

**Remove gradients (lines 23-26):**
```css
/* DELETE THESE LINES */
background: linear-gradient(45deg, #00ff88, #00bbff);
-webkit-background-clip: text;
-webkit-text-fill-color: transparent;
background-clip: text;
```

**Make it simple:**
```css
.hero-title {
  font-size: 3rem;
  color: #333333;  /* Simple black text */
}
```

### Change Button Styles
**File:** `src/pages/Pages.css`

**Find `.btn-primary` and change:**
```css
.btn-primary {
  background: #007bff;  /* Simple blue */
  color: white;
  border: none;
}
```

## 📁 File Map - Where Everything Lives

```
src/
├── pages/              ← YOUR CONTENT
│   ├── Home.tsx        ← Homepage text
│   ├── About.tsx       ← About page text
│   ├── Portfolio.tsx   ← Portfolio text
│   ├── Contact.tsx     ← Contact form
│   └── Pages.css       ← How pages look
├── components/         ← REUSABLE PARTS
│   ├── Navigation.tsx  ← Menu/header
│   └── Navigation.css  ← Menu styling
├── styles/
│   └── themes.css      ← Colors and themes
└── App.css            ← Global styles
```

## 🚀 See Your Changes

1. Run: `npm start`
2. Open: http://localhost:3000
3. Edit files and save
4. Browser updates automatically

## 📤 Deploy Your Changes

1. `git add .`
2. `git commit -m "Updated website"`
3. `git push origin main`
4. Wait 5-10 minutes
5. Check: https://xclr8er.com

---

## 🔧 Technical Info (For Developers)

This is a React SPA that deploys to AWS Amplify via CodeCatalyst.

### Local Development
```bash
npm start          # Run locally
npm test           # Run tests
npm run build      # Build for production
```

### Deployment
- Pushes to `main` branch trigger automatic deployment
- CodeCatalyst workflow builds and deploys to AWS
- Live site: https://xclr8er.com

### Project Structure
- **React** - Frontend framework
- **React Router** - Page navigation
- **CSS Variables** - Theme system
- **AWS Amplify** - Hosting
- **CodeCatalyst** - CI/CD pipeline

---

# Getting Started with Create React App

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

## Available Scripts

In the project directory, you can run:

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.\
You will also see any lint errors in the console.

### `npm test`

Launches the test runner in the interactive watch mode.\
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

### `npm run eject`

**Note: this is a one-way operation. Once you `eject`, you can’t go back!**

If you aren’t satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you’re on your own.

You don’t have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn’t feel obligated to use this feature. However we understand that this tool wouldn’t be useful if you couldn’t customize it when you are ready for it.

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).
