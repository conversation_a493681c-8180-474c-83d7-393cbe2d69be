{"name": "reactts", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.3", "@types/react": "^18.0.24", "@types/react-dom": "^18.0.8", "jest-junit": "^14.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.8.4", "web-vitals": "^2.1.4"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"collectCoverageFrom": ["**/*.{ts,tsx}", "!**/node_modules/**", "!**/build/**", "!**/definitionfiles/**", "!**/*.mock.ts", "!src/setupTests.ts"], "coverageReporters": ["lcov"]}, "jest-junit": {"outputName": "coverage/junit.xml"}, "overrides": {"webpack": "5.82.1", "@babel/core": "^7.22.0"}}