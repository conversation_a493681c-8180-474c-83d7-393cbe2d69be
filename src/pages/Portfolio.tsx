import React from 'react';
import './Pages.css';

const Portfolio: React.FC = () => {
  const projects = [
    {
      id: 1,
      title: "AI-Powered Analytics",
      description: "Advanced data analysis platform with machine learning capabilities",
      tech: ["React", "Python", "TensorFlow", "AWS"]
    },
    {
      id: 2,
      title: "Smart Automation Suite",
      description: "Comprehensive automation tools for business process optimization",
      tech: ["Node.js", "Docker", "Kubernetes", "MongoDB"]
    },
    {
      id: 3,
      title: "Real-time Collaboration Platform",
      description: "Seamless team collaboration with real-time synchronization",
      tech: ["React", "WebSocket", "Redis", "PostgreSQL"]
    }
  ];

  return (
    <div className="page">
      <div className="container">
        <h1>Our Portfolio</h1>
        <p className="lead">
          Explore our latest projects and innovations that showcase our commitment 
          to excellence and cutting-edge technology.
        </p>
        
        <div className="portfolio-grid">
          {projects.map((project) => (
            <div key={project.id} className="portfolio-card">
              <h3>{project.title}</h3>
              <p>{project.description}</p>
              <div className="tech-stack">
                {project.tech.map((tech, index) => (
                  <span key={index} className="tech-tag">{tech}</span>
                ))}
              </div>
              <button className="btn btn-outline">View Details</button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Portfolio;
