import React from 'react';
import './Pages.css';

const Home: React.FC = () => {
  return (
    <div className="page">
      <div className="hero-section">
        <div className="container">
          <h1 className="hero-title">Welcome to My Awesome Company</h1>
          <p className="hero-subtitle">Building Amazing Digital Experiences</p>
          <p className="hero-description">
            We're building the future of intelligent systems, creating solutions that 
            transform ideas into reality. Join us on our journey to accelerate innovation.
          </p>
          <div className="hero-actions">
            <button className="btn btn-primary">Get Started</button>
            <button className="btn btn-secondary">Learn More</button>
          </div>
        </div>
      </div>
      
      <div className="features-section">
        <div className="container">
          <h2>What We Do</h2>
          <div className="features-grid">
            <div className="feature-card">
              <h3>Innovation</h3>
              <p>Cutting-edge solutions for modern challenges</p>
            </div>
            <div className="feature-card">
              <h3>Technology</h3>
              <p>Leveraging the latest tools and frameworks</p>
            </div>
            <div className="feature-card">
              <h3>Excellence</h3>
              <p>Delivering quality results that exceed expectations</p>
            </div>
          </div>
        </div>
      </div>

      <div className="stats-section">
        <div className="container">
          <h2>Our Impact</h2>
          <div className="stats-grid">
            <div className="stat-item">
              <h3>500+</h3>
              <p>Projects Completed</p>
            </div>
            <div className="stat-item">
              <h3>50+</h3>
              <p>Happy Clients</p>
            </div>
            <div className="stat-item">
              <h3>5+</h3>
              <p>Years Experience</p>
            </div>
            <div className="stat-item">
              <h3>24/7</h3>
              <p>Support Available</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
