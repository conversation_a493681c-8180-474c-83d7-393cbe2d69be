/* Theme Variables */
:root {
  /* Light Theme (Default) */
  --bg-primary: #f8f9fa;
  --bg-secondary: #ffffff;
  --bg-hero: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-light: #ffffff;
  --border-color: #e0e0e0;
  --shadow: rgba(0, 0, 0, 0.1);
  --accent-gradient: linear-gradient(45deg, #00ff88, #00bbff);
  --card-bg: #ffffff;
  --nav-bg: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
}

/* Dark Theme */
[data-theme="dark"] {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-hero: linear-gradient(135deg, #0f1419 0%, #1a2332 100%);
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-light: #ffffff;
  --border-color: #404040;
  --shadow: rgba(0, 0, 0, 0.3);
  --accent-gradient: linear-gradient(45deg, #00ff88, #00bbff);
  --card-bg: #2d2d2d;
  --nav-bg: linear-gradient(135deg, #0f1419 0%, #1a2332 100%);
}

/* Apply theme variables */
body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.hero-section {
  background: var(--bg-hero);
}

.feature-card,
.portfolio-card,
.contact-form {
  background: var(--card-bg);
  box-shadow: 0 4px 20px var(--shadow);
}

.navigation {
  background: var(--nav-bg);
}

.form-group input,
.form-group textarea {
  background: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.value-item {
  background: var(--bg-secondary);
  border-left-color: #2a5298;
}

/* Theme Toggle Button */
.theme-toggle {
  background: none;
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: var(--text-light);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.theme-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}
